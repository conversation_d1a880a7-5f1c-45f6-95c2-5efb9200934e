import 'dart:io';

import '../builder/llm_builder.dart';
import '../core/chat_provider.dart';
import '../models/chat_models.dart';

/// Example demonstrating how to use reasoning models (o1, o3, o4 series) with thinking support
///
/// 使用说明：
/// 1. 注释掉其中一个示例来单独测试
/// 2. streamingExample() - 流式输出示例，可以实时看到思考过程
/// 3. nonStreamingExample() - 非流式输出示例，一次性返回完整结果
void main() async {
  // 测试流式输出 - 注释掉下面这行来禁用流式测试
  // await streamingExample();

  print('\n' + '=' * 80 + '\n');

  // 测试非流式输出 - 注释掉下面这行来禁用非流式测试
  await nonStreamingExample();
}

/// 流式输出示例 - 可以实时看到AI的思考过程
Future<void> streamingExample() async {
  print('🌊 流式输出示例 (Streaming Example)');
  print('=' * 50);

  // Get OpenAI API key from environment variable or use test key as fallback
  final apiKey = Platform.environment['OPENAI_API_KEY'] ?? 'sk-OPENAI';

  // Initialize and configure the LLM client for streaming
  final llm = await LLMBuilder()
      .backend(LLMBackend.openai) // Use OpenAI as the LLM provider
      .apiKey(apiKey) // Set the API key
      .baseUrl('https://api.mnapi.com/v1/')
      .model('deepseek-r1') // Use reasoning model
      .reasoningEffort('high') // Set reasoning effort level
      .maxTokens(2000) // Limit response length
      .stream(true) // Enable streaming to see thinking process
      .build();

  // Create a complex reasoning task
  final messages = [
    ChatMessage.user(
      'Solve this step by step: A farmer has 17 sheep. All but 9 die. How many sheep are left? '
      'Please think through this carefully and explain your reasoning.',
    ),
  ];

  try {
    // Check if provider supports streaming
    if (llm is StreamingChatProvider) {
      print('🧠 开始推理模型对话，支持思考过程显示...\n');

      var thinkingContent = StringBuffer();
      var responseContent = StringBuffer();
      var isThinking = true;

      // Send streaming chat request and handle events
      await for (final event in llm.chatStream(messages)) {
        switch (event) {
          case ThinkingDeltaEvent(delta: final delta):
            // Collect thinking/reasoning content
            thinkingContent.write(delta);
            print('\x1B[90m$delta\x1B[0m'); // Gray color for thinking content
            break;
          case TextDeltaEvent(delta: final delta):
            // This is the actual response after thinking
            if (isThinking) {
              print('\n\n🎯 最终回答:');
              isThinking = false;
            }
            responseContent.write(delta);
            print(delta);
            break;
          case ToolCallDeltaEvent(toolCall: final toolCall):
            // Handle tool call events (if supported)
            print('\n[工具调用: ${toolCall.function.name}]');
            break;
          case CompletionEvent(response: final response):
            // Handle completion
            print('\n\n✅ 推理完成!');

            if (response.usage != null) {
              final usage = response.usage!;
              print(
                '\n📊 使用情况: ${usage.promptTokens} 提示词 + ${usage.completionTokens} 完成 = ${usage.totalTokens} 总计 tokens',
              );
            }
            break;
          case ErrorEvent(error: final error):
            // Handle errors
            print('\n❌ 流式错误: $error');
            break;
        }
      }

      // Summary
      print('\n📝 流式输出总结:');
      print('思考内容长度: ${thinkingContent.length} 字符');
      print('回答内容长度: ${responseContent.length} 字符');
    } else {
      print('❌ 提供商不支持流式输出');
    }
  } catch (e) {
    print('❌ 流式示例错误: $e');
  }
}

/// 非流式输出示例 - 一次性返回完整结果
Future<void> nonStreamingExample() async {
  print('📄 非流式输出示例 (Non-Streaming Example)');
  print('=' * 50);

  // Get OpenAI API key from environment variable or use test key as fallback
  final apiKey = Platform.environment['OPENAI_API_KEY'] ?? 'sk-OPENAI';

  // Initialize and configure the LLM client for non-streaming
  final llm = await LLMBuilder()
      .backend(LLMBackend.openai) // Use OpenAI as the LLM provider
      .apiKey(apiKey) // Set the API key
      .baseUrl('https://api.mnapi.com/v1/')
      .model('deepseek-r1') // Use reasoning model
      .reasoningEffort('high') // Set reasoning effort level
      .maxTokens(2000) // Limit response length
      .stream(false) // Disable streaming for complete response
      .build();

  // Create a complex reasoning task
  final messages = [ChatMessage.user('hello')];

  try {
    print('🧠 开始推理模型对话，等待完整回答...\n');

    // Send non-streaming chat request
    final response = await llm.chat(messages);

    // Show thinking content if available
    if (response.thinking != null && response.thinking!.isNotEmpty) {
      print('🧠 思考过程:');
      print(
        '\x1B[90m${response.thinking}\x1B[0m',
      ); // Gray color for thinking content
      print('\n' + '-' * 50 + '\n');
    }

    // Show the final response
    print('🎯 最终回答:');
    print(response.text);

    // Show usage information
    if (response.usage != null) {
      final usage = response.usage!;
      print(
        '\n📊 使用情况: ${usage.promptTokens} 提示词 + ${usage.completionTokens} 完成 = ${usage.totalTokens} 总计 tokens',
      );
    }

    print('\n📝 非流式输出总结:');
    print('思考内容长度: ${response.thinking?.length ?? 0} 字符');
    print('回答内容长度: ${response.text?.length} 字符');
  } catch (e) {
    print('❌ 非流式示例错误: $e');
  }
}
